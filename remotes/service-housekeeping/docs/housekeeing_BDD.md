**Business Design Document - House Keeping Service**

**1. Purpose** Enable users to book house keeping services via a mobile app, allowing them to specify location, property type, room types and quantities, arrangement preferences, schedule, and payment method for professional cleaners to provide comprehensive house cleaning services.

**2. Scope / Features**

* **Choose an Address:**
  * Users can select their house address from saved locations or add a new address.
  * Support for house/apartment/villa type selection
  * Users provide name of place, contact information for new address.
  * Support for GPS location, manual address entry, or map selection.
  * Complete address validation and contact information management
* **Choose Task Details:**
  * **Select Service Type:** Users choose the service type as Hotel/Homestay, Serviced apartment, Villa/House
  * **Select Room Type:** Users choose the type and quantity of rooms to be cleaned (e.g., Single Room, Double Room, Family Room,…).
  * **Request to Arrange Room According to Pictures:** Optional add-on service where users can upload illustrative images for room arrangement. This adds +15 minutes per room. Users have the option to capture a new photo or select an existing photo from their photo library.
  * **Clean the Lobby and Common Areas:** Optional add-on service for cleaning shared spaces, adding +45 minutes.
* **Choose Working Time:**
  * **Date Selection:** Calendar view showing the current date and the next 6 days.
  * **Time Selection:** Time picker with AM/PM options .
  * **Weekly Schedule:** Toggle for recurring weekly appointments with selectable days (Sun-Sat).
  * **Notes for Tasker:**
    * Optional text input field for users to provide custom instructions or special requests to the cleaner.
    * This field is **optional** (can be left blank).
    * Input is **limited to a maximum of 400 characters**.
* **Confirm and Payment:**
  * Complete summary of booking displayed before confirmation.
  * Location details, task information, and payment breakdown.
  * Users can **edit their Contact Name and Phone Number** directly from this screen.
  * Users can choose payment method or apply promotion

**3. Stakeholders**

* **End Users:** Homeowners, renters, or individuals booking house keeping services.
* **Cleaners:** Professional house cleaning service providers (Taskers).
* **Admin:** Service quality oversight, scheduling coordination, and pricing management.

**4. Functional Requirements**

**4.1 Choose Location**

* User must select or input a valid service address with house type.
* Users can add new addresses using GPS, manual entry, or map selection.
* Address validation and formatting support.
* Address change functionality available.
* Option to save address as default for future bookings.

**4.2 Select Service Type**

* **Select Service Type:** Users choose the service type as Hotel/Homestay, Serviced apartment, Villa/House

**4.3 Choose Task Details**

* **Select Service Type:** Users choose the service type as Hotel/Homestay, Serviced apartment, Villa/House
* User can select multiple room types (Single room, Double room, Family room, Dormitory room <8 beds, Dormitory room >8 beds) and specify the quantity for each.
* Room type selection should dynamically influence estimated time and pricing.
* **Request to Arrange Room According to Pictures:**
  * Option to "Take a photo" or "Library" for each room type.
  * Increases cleaning time by +15 minutes per room.
  * User can specify "Number of rooms to be arranged according to the pictures" using +/- buttons.
* **Clean the Lobby and Common Areas:**
  * Toggle switch to enable/disable this add-on.
  * Adds +45 minutes to the total service duration.

**4.3 Choose Working Time**

* **Date Selection:** Calendar view allowing selection from the current date and the next 7 consecutive days. The current day should be highlighted. Future dates should be selectable.
* **Time Selection:** Time picker with AM/PM format.
* **Weekly Schedule:** Toggle switch to enable recurring appointments. When enabled, users can select specific days of the week (Sun-Sat). Selected days should be highlighted (e.g., Orange highlight for Sun, Tue, Fri).

**Notes for Tasker**

* Text input field for custom instructions.
* Help text: "This note will help Tasker do a better job."
* **Validation**: The **Notes field** is **optional** (can be left empty) and **limited to a maximum of 400 characters**.

**4.5 Confirm and Payment**

* **Payment Options**: System supports multiple payment methods:
  * **Cash**: Pay directly upon task completion.
  * **Promotion Codes**: Apply valid vouchers to reduce total cost.
  * **bPay**, **Momo**, **ZaloPay**, **ShopeePay**: Supported e-wallets.
  * **VNPAY / VietQR**: QR payment via banking apps.
  * **Visa/MasterCard**: Manual card entry with 3D Secure authentication.
  * **Kredivo**: Option to pay later or in installments.
* **Promotion Handling**:
  * Users can view available promotions.
  * Valid codes are applied and reflected in total price.
* **Dynamic Pricing**:
  * Real-time price calculation based on:
    * Selected room type and quantity.
    * Add-on services (e.g. room arrangement, common areas).
    * Estimated working time and demand-based rate.
* **Pricing Display**:
  * Itemized cost breakdown.
  * Final total clearly shown before confirmation.
* **Booking Summary**:
  * Displays full details including:
    * Contact info
    * Selected date & time
    * Service details & notes
    * Applied promotions
    * Selected payment method
* **Validation**:
  * “Book” button enabled only when all required data is completed and valid.
  * Input fields (e.g. contact name, phone) validated in real time.
* **Confirmation Flow**:
  * Upon tapping “Book”, system processes the booking.
  * If successful, navigate user to booking confirmation screen.

**5. Non-Functional Requirements**

* Mobile-first responsive design optimized for house keeping booking.
* Vietnamese language support with a clear, professional interface.
* Real-time validation and user feedback.
* Secure handling of personal and payment information.
* Reliable cleaner matching and scheduling system.
* Performance optimization for pricing calculations and UI responsiveness.
* Accessibility features for all users.

**6. Acceptance Criteria (Testable Steps)**

**6.1 Choose Location**

* **Initial Display:** When users start the booking flow, the "List of places" screen is displayed.
* **Existing Location Selection:** Users can select an already saved house address from the "List of places" if available.
* **Adding a New Location (Initial Step):** Users can tap the "Add a location" button on the "List of places" screen to proceed.
* **Choose an Address Screen:** Tapping "Add a location" navigates to the "Choose an address" screen, which lists previously used addresses that might not have a specific 'name of place' assigned.
  * Users can select one of these existing addresses to assign a 'name of place'.
  * Alternatively, users can tap the "Add a new address" button to enter a completely new address.
* **Choose Location Screen (New Address Entry):** Tapping "Add a new address" navigates to the "Choose location" screen, where users can add a new location by:
  * Using GPS to automatically detect their current location.
  * Manually entering full address details.
  * Tapping on the map and confirming with "Pick this location."
* **Location Detail Input & Confirmation:** After selecting or adding a location, users are directed to a screen to:
  * Choose the property type from predefined options: "Hotel/Homestay," "Serviced apartment," or "Villa/House."
  * Input complete address details including:
    * "Name of place" (e.g., "My Home," "Grandma's House").
    * "Address description" (e.g., street number, specific directions).
    * "Contact name."
    * "Phone number" (validated for Vietnamese format).
  * Users can tap "OK" (or "Continue") to confirm these address and contact details.
  * Users may edit all address and contact fields before final confirmation.
  * An option "Set this address as my default address" is available to save the location for future bookings.
* **Return to List of Places:** After confirming the details for a newly added or named address, the user is returned to the "List of places" screen, where the newly added/named location is now visible.
* **Proceed to Service Type Selection:** Users must then select one of the addresses from the "List of places" to proceed to the "Select service type" screen.

**6.2 Select Service Type/ Task Details**

* **Select Service Type:** Users choose the service type as Hotel/Homestay, Serviced apartment, Villa/House
* User can select multiple room types: "Single room," "Double room," "Family room," "Dormitory room <8 beds," "Dormitory room >8 beds" and adjust quantities using +/- buttons.
* The "Request to arrange the room according to the pictures" section:
  * Allows users to tap "Take a picture" to open a modal with "Library" and "Take a photo" options.
  * "Number of rooms to be arranged according to the pictures" counter updates correctly.
  * The text "+15 minutes per room" is displayed clearly.
* The "Options" section:
  * Toggle switch for "Clean the lobby and common areas" functions correctly.
  * The text "+45 minutes" is displayed when the option is active.
* The total estimated cost and duration at the bottom of the screen (e.g., "216,000 VND / 2h") updates dynamically based on selections.

**6.3 Choose Working Time**

* The "Working time" section displays a calendar view with the current week, correctly highlighting the current day).
* Users can select future dates.
* The "Time" picker allows users to select hours and minutes with AM/PM.
* The "Weekly Schedule" toggle enables/disables the day selection row.
* When "Weekly Schedule" is enabled, users can select/deselect days (Sun-Sat), and selected days are highlighted (e.g., orange).
* The total price and duration displayed at the bottom updates dynamically with date and time selections.
* The "Notes for Tasker" text area allows free-form text input. It should allow being empty. The user can enter 400 characters, no additional input is accepted.

**6.4 Confirmation and Payment**

**6.4.1 Payment Method Selection**

* **Supported Methods**: The system supports the following payment methods:
  * Cash
  * Electronic payments (bPay, Momo, ZaloPay, ShopeePay, VietQR, VNPAY, Visa/Master, Pay later/Installment via Kredivo)
* **Selection Flow**:
  * When user opens the Payment Method screen, all available methods are displayed.
  * Once a method is selected, the system stores the selection and navigates the user back to the **Confirm & Pay** screen.
  * If Visa/Master is selected and no card is saved, the system navigates to the **Add Card** screen for user input.
* **Promotion Code Handling**:
  * User may enter a promotion code or select from available vouchers.
  * If a valid promotion code is applied, the system:
    * Updates the discount
    * Recalculates the total price in the booking summary
    * Displays the promotion code and corresponding discount value
  * If the promotion code is invalid:
    * A notification is shown: **“The promotion code is invalid. Please check again.”**
    * No price change is applied

**6.4.2 Booking Summary & Confirmation**

* The booking summary displays all essential details, including:
  * Location of service.
  * Client contact information with "Change" button.
  * Selected date, time, and estimated duration of the task.
  * Description of the task (selected room types, quantities, add-ons).
  * Payment Details section shows: Service Cost, Total Payment.
  * Payment method options: Cash option with icon, Promotion option with icon.
* Final total displayed prominently.
* "Book" button available for final confirmation only when all required information is complete.

**6.5 Error Handling & Validation**

* Past date/time selection shows appropriate error message.
* Incomplete address fields prevent progression to the next step.
* Invalid or unsupported address shows validation error.
* Invalid contact information displays validation errors.
* Network errors during booking show retry options.
* Payment method selection required before final booking.

**7. Open Questions / Assumptions**

* How are estimated durations for each room type calculated (e.g., 15 sqm for single room)? Are these fixed or customizable?
* What is the process if a user wants to cancel a scheduled cleaning?
* Can users reschedule cleaning appointments? What is the cut-off time for rescheduling?
* How do we handle access to properties (e.g., key pickup, gate codes)?
* What happens if cleaners encounter unexpected issues (e.g., pet waste, extreme clutter, damage)?
* Will users be able to rate and review individual cleaners?
* Can users request specific cleaners or teams for consistency?
* What is the policy for cleaning during public holidays or weekends?
* What is the maximum number of rooms that can be selected for each room type?

**8. Visual Reference** Screens and UI flows as shown in the provided mobile app images:

* Address selection with search and saved locations.
* Property type selection (Hotel/Homestay, Serviced apartment, Villa/House).
* Room type and quantity selection (Single room, Double room, Family room, Dormitory room <8 beds, Dormitory room >8 beds).
* Request to arrange room by pictures with photo upload options.
* Add-on services (Clean the lobby and common areas).
* Calendar scheduling with date and time picker.
* Weekly schedule toggle with day selection.
* Notes for Tasker input.
* Dynamic pricing display.

